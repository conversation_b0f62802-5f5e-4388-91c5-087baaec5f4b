import { useQuery } from "@tanstack/react-query";
import {
  getNotices,
  getTrainingSessions,
  getTrainingSessionById,
  getPermissions,
  getRoles,
} from "./api";

// Notice Queries
export function useGetNoticesQuery() {
  return useQuery({
    queryKey: ["notices"],
    queryFn: getNotices,
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 30 * 60 * 1000, // 30 minutes
  });
}

// Training Queries
export function useGetTrainingSessionsQuery() {
  return useQuery({
    queryKey: ["training-sessions"],
    queryFn: getTrainingSessions,
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 30 * 60 * 1000, // 30 minutes
  });
}

export function useGetTrainingSessionByIdQuery(id) {
  return useQuery({
    queryKey: ["training-session", id],
    queryFn: () => getTrainingSessionById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 30 * 60 * 1000, // 30 minutes
  });
}

// Permission Queries
export function useGetPermissionsQuery() {
  return useQuery({
    queryKey: ["permissions"],
    queryFn: getPermissions,
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 30 * 60 * 1000, // 30 minutes
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    onError: (error) => {
      console.error("Failed to fetch permissions:", error);
    },
  });
}

// Role Queries
export function useGetRolesQuery() {
  return useQuery({
    queryKey: ["roles"],
    queryFn: getRoles,
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 30 * 60 * 1000, // 30 minutes
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    onError: (error) => {
      console.error("Failed to fetch roles:", error);
    },
  });
}
