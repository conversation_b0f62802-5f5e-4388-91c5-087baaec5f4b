/* eslint-disable react/prop-types */
const StepSection = ({ title, reverse, stepImg, steps, step }) => {
  return (
    <div
      className={`flex flex-col gap-8 md:gap-12 lg:gap-16 ${
        reverse ? "md:flex-row-reverse" : "md:flex-row"
      } mt-8`}
    >
      <div className="md:w-1/2 flex items-center justify-center px-4 md:px-6 lg:px-8">
        <div>
          <h1 className="font-poppins text-heading6 md:text-heading5 leading-tight md:leading-[1.625rem] font-semibold mb-3">
            <span className="text-primary mr-3">{`#${step}`}</span>
            {title}
          </h1>
          <ul className="list-disc pl-5 space-y-2 marker:text-primary ml-4">
            {steps.map((step, index) => (
              <li key={index} className="relative text-bodyLarge text-darkTeal">
                {step}
              </li>
            ))}
          </ul>
        </div>
      </div>

      <img
        src={stepImg}
        alt="step-img"
        className="md:w-1/2 w-full h-[20rem] md:h-[30rem] lg:h-[35rem] mt-4 md:mt-0"
      />
    </div>
  );
};

export default StepSection;
