import { useState } from "react";
import { Plus, Search, Pencil, Trash2, Upload, Loader2 } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { toast } from "@/components/ui/use-toast";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  useGetCategories,
  useCreateSubCategory,
  useUpdateSubCategory,
  useDeleteSubCategory
} from "@/services/admin/query";

export default function SubcategoriesPage() {
  // API hooks
  const { data: categoriesData, isLoading, error } = useGetCategories();
  const createSubCategoryMutation = useCreateSubCategory();
  const updateSubCategoryMutation = useUpdateSubCategory();
  const deleteSubCategoryMutation = useDeleteSubCategory();

  // Extract categories and subcategories from API response
  const allCategories = categoriesData?.data || [];
  const categories = allCategories.filter(cat => !cat.parent_id);
  const subcategories = allCategories.filter(cat => cat.parent_id);

  // State
  const [searchQuery, setSearchQuery] = useState("");
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [currentSubcategory, setCurrentSubcategory] = useState({
    name: "",
    description: "",
    image: "/placeholder.svg?height=40&width=40",
    parent_id: "",
  });
  const [imagePreview, setImagePreview] = useState(null);

  // Helper function to get category name by ID
  const getCategoryNameById = (categoryId) => {
    const category = categories.find((cat) => cat.id === parseInt(categoryId));
    return category ? category.name : "Unknown Category";
  };

  // Filter subcategories based on search query
  const filteredSubcategories = subcategories.filter(
    (subcategory) =>
      subcategory.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (subcategory.description || "")
        .toLowerCase()
        .includes(searchQuery.toLowerCase()) ||
      getCategoryNameById(subcategory.parent_id)
        .toLowerCase()
        .includes(searchQuery.toLowerCase())
  );

  // Handle image upload
  const handleImageChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      // In a real app, you would upload this to a server/storage
      // For this demo, we'll just create a local URL
      const imageUrl = URL.createObjectURL(file);
      setCurrentSubcategory({ ...currentSubcategory, image: imageUrl });
      setImagePreview(imageUrl);
    }
  };

  // Handle adding a new subcategory
  const handleAddSubcategory = async (e) => {
    e.preventDefault();
    if (!currentSubcategory.parent_id) {
      toast({
        title: "Error",
        description: "Please select a category",
        variant: "destructive",
      });
      return;
    }

    try {
      await createSubCategoryMutation.mutateAsync({
        name: currentSubcategory.name,
        description: currentSubcategory.description,
        parent_id: parseInt(currentSubcategory.parent_id),
      });
      setCurrentSubcategory({
        name: "",
        description: "",
        image: "/placeholder.svg?height=40&width=40",
        parent_id: "",
      });
      setImagePreview(null);
      setIsAddDialogOpen(false);
      toast({
        title: "Sub-category added",
        description: `${currentSubcategory.name} has been added successfully.`,
      });
    } catch {
      toast({
        title: "Error",
        description: "Failed to add sub-category. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Handle editing a subcategory
  const handleEditSubcategory = async (e) => {
    e.preventDefault();
    if (!currentSubcategory.parent_id) {
      toast({
        title: "Error",
        description: "Please select a category",
        variant: "destructive",
      });
      return;
    }

    try {
      await updateSubCategoryMutation.mutateAsync({
        id: currentSubcategory.id,
        subCategoryData: {
          name: currentSubcategory.name,
          description: currentSubcategory.description,
          parent_id: parseInt(currentSubcategory.parent_id),
        }
      });
      setImagePreview(null);
      setIsEditDialogOpen(false);
      toast({
        title: "Sub-category updated",
        description: `${currentSubcategory.name} has been updated successfully.`,
      });
    } catch {
      toast({
        title: "Error",
        description: "Failed to update sub-category. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Handle deleting a subcategory
  const handleDeleteSubcategory = async () => {
    try {
      await deleteSubCategoryMutation.mutateAsync(currentSubcategory.id);
      setIsDeleteDialogOpen(false);
      toast({
        title: "Sub-category deleted",
        description: `${currentSubcategory.name} has been deleted successfully.`,
      });
    } catch {
      toast({
        title: "Error",
        description: "Failed to delete sub-category. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Open edit dialog and set current subcategory
  const openEditDialog = (subcategory) => {
    setCurrentSubcategory({
      ...subcategory,
      parent_id: subcategory.parent_id.toString(),
    });
    setImagePreview(subcategory.image);
    setIsEditDialogOpen(true);
  };

  // Open delete dialog and set current subcategory
  const openDeleteDialog = (subcategory) => {
    setCurrentSubcategory(subcategory);
    setIsDeleteDialogOpen(true);
  };

  return (
    <div className="container mx-auto py-10">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Subcategories</h1>
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button className="text-white">
              <Plus className="mr-2 h-4 w-4" />
              Add Subcategory
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Add New Subcategory</DialogTitle>
              <DialogDescription>
                Enter the details for the new subcategory.
              </DialogDescription>
            </DialogHeader>
            <form onSubmit={handleAddSubcategory}>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="category" className="text-right">
                    Category
                  </Label>
                  <div className="col-span-3">
                    <Select
                      value={currentSubcategory.parent_id}
                      onValueChange={(value) =>
                        setCurrentSubcategory({
                          ...currentSubcategory,
                          parent_id: value,
                        })
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select a category" />
                      </SelectTrigger>
                      <SelectContent>
                        {categories.map((category) => (
                          <SelectItem
                            key={category.id}
                            value={category.id.toString()}
                          >
                            {category.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="name" className="text-right">
                    Name
                  </Label>
                  <Input
                    id="name"
                    value={currentSubcategory.name}
                    onChange={(e) =>
                      setCurrentSubcategory({
                        ...currentSubcategory,
                        name: e.target.value,
                      })
                    }
                    className="col-span-3"
                    required
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="description" className="text-right">
                    Description
                  </Label>
                  <Input
                    id="description"
                    value={currentSubcategory.description}
                    onChange={(e) =>
                      setCurrentSubcategory({
                        ...currentSubcategory,
                        description: e.target.value,
                      })
                    }
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="image" className="text-right">
                    Image
                  </Label>
                  <div className="col-span-3">
                    <div className="flex items-center gap-4">
                      <div className="relative h-16 w-16 rounded-md overflow-hidden border">
                        <img
                          src={imagePreview || currentSubcategory.image}
                          alt="Subcategory image preview"
                          className="object-cover w-full h-full"
                        />
                      </div>
                      <div className="flex-1">
                        <Label
                          htmlFor="image-upload"
                          className="cursor-pointer"
                        >
                          <div className="flex items-center gap-2 border rounded-md p-2 hover:bg-muted">
                            <Upload className="h-4 w-4" />
                            <span>Upload Image</span>
                          </div>
                          <Input
                            id="image-upload"
                            type="file"
                            accept="image/*"
                            className="hidden"
                            onChange={handleImageChange}
                          />
                        </Label>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <DialogFooter>
                <Button type="submit" disabled={createSubCategoryMutation.isPending}>
                  {createSubCategoryMutation.isPending ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                      Creating...
                    </>
                  ) : (
                    'Save Subcategory'
                  )}
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      <div className="mb-6">
        <div className="relative">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search subcategories..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Image</TableHead>
              <TableHead>ID</TableHead>
              <TableHead>Name</TableHead>
              <TableHead>Category</TableHead>
              <TableHead className="hidden md:table-cell">
                Description
              </TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell colSpan={6} className="text-center py-8">
                  <Loader2 className="h-6 w-6 animate-spin mx-auto" />
                  <p className="mt-2 text-muted-foreground">Loading sub-categories...</p>
                </TableCell>
              </TableRow>
            ) : error ? (
              <TableRow>
                <TableCell colSpan={6} className="text-center py-8 text-red-600">
                  Error loading sub-categories. Please try again.
                </TableCell>
              </TableRow>
            ) : filteredSubcategories.length > 0 ? (
              filteredSubcategories.map((subcategory) => (
                <TableRow key={subcategory.id}>
                  <TableCell>
                    <div className="relative h-10 w-10 rounded-md overflow-hidden">
                      <img
                        src={subcategory.image || "/placeholder.svg"}
                        alt={subcategory.name}
                        className="object-cover w-full h-full"
                      />
                    </div>
                  </TableCell>
                  <TableCell className="font-medium">
                    {subcategory.id}
                  </TableCell>
                  <TableCell>{subcategory.name}</TableCell>
                  <TableCell>
                    {getCategoryNameById(subcategory.parent_id)}
                  </TableCell>
                  <TableCell className="hidden md:table-cell">
                    {subcategory.description}
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-2">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => openEditDialog(subcategory)}
                      >
                        <Pencil className="h-4 w-4" />
                        <span className="sr-only">Edit {subcategory.name}</span>
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="text-destructive"
                        onClick={() => openDeleteDialog(subcategory)}
                      >
                        <Trash2 className="h-4 w-4" />
                        <span className="sr-only">
                          Delete {subcategory.name}
                        </span>
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={6} className="h-24 text-center">
                  No subcategories found.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Edit Subcategory Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Subcategory</DialogTitle>
            <DialogDescription>
              Update the details for this subcategory.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleEditSubcategory}>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit-category" className="text-right">
                  Category
                </Label>
                <div className="col-span-3">
                  <Select
                    value={currentSubcategory.parent_id}
                    onValueChange={(value) =>
                      setCurrentSubcategory({
                        ...currentSubcategory,
                        parent_id: value,
                      })
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select a category" />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.map((category) => (
                        <SelectItem
                          key={category.id}
                          value={category.id.toString()}
                        >
                          {category.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit-name" className="text-right">
                  Name
                </Label>
                <Input
                  id="edit-name"
                  value={currentSubcategory.name}
                  onChange={(e) =>
                    setCurrentSubcategory({
                      ...currentSubcategory,
                      name: e.target.value,
                    })
                  }
                  className="col-span-3"
                  required
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit-description" className="text-right">
                  Description
                </Label>
                <Input
                  id="edit-description"
                  value={currentSubcategory.description}
                  onChange={(e) =>
                    setCurrentSubcategory({
                      ...currentSubcategory,
                      description: e.target.value,
                    })
                  }
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit-image" className="text-right">
                  Image
                </Label>
                <div className="col-span-3">
                  <div className="flex items-center gap-4">
                    <div className="relative h-16 w-16 rounded-md overflow-hidden border">
                      <img
                        src={imagePreview || currentSubcategory.image}
                        alt="Subcategory image preview"
                        className="object-cover w-full h-full"
                      />
                    </div>
                    <div className="flex-1">
                      <Label
                        htmlFor="edit-image-upload"
                        className="cursor-pointer"
                      >
                        <div className="flex items-center gap-2 border rounded-md p-2 hover:bg-muted">
                          <Upload className="h-4 w-4" />
                          <span>Upload Image</span>
                        </div>
                        <Input
                          id="edit-image-upload"
                          type="file"
                          accept="image/*"
                          className="hidden"
                          onChange={handleImageChange}
                        />
                      </Label>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button type="submit" disabled={updateSubCategoryMutation.isPending}>
                {updateSubCategoryMutation.isPending ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    Updating...
                  </>
                ) : (
                  'Update Subcategory'
                )}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Delete Subcategory Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Subcategory</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this subcategory? This action
              cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
              disabled={deleteSubCategoryMutation.isPending}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteSubcategory}
              disabled={deleteSubCategoryMutation.isPending}
            >
              {deleteSubCategoryMutation.isPending ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  Deleting...
                </>
              ) : (
                'Delete'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
