# Category and Sub-Category API Implementation Guide

## Overview
This guide explains how to use the complete CRUD operations for categories and sub-categories using the `/admin/business-category` endpoint.

## API Endpoints

### Base Endpoint
- **Base URL**: `/admin/business-category`

### Category Operations
- **GET** `/admin/business-category` - Fetch all categories and sub-categories
- **POST** `/admin/business-category` - Create a new category
- **PUT** `/admin/business-category/{id}` - Update an existing category
- **DELETE** `/admin/business-category/{id}` - Delete a category

### Sub-Category Operations
- **GET** `/admin/business-category` - Fetch all categories and sub-categories (same as categories)
- **POST** `/admin/business-category` - Create a new sub-category (with `parent_id` in body)
- **PUT** `/admin/business-category/{id}` - Update an existing sub-category (with `parent_id` in body)
- **DELETE** `/admin/business-category/{id}` - Delete a sub-category

## API Functions

### Category Functions

#### 1. Get Categories
```javascript
import { getCategories } from '@/services/admin/api';

const categories = await getCategories();
```

#### 2. Create Category
```javascript
import { createCategory } from '@/services/admin/api';

const categoryData = {
  name: "Electronics",
  description: "Electronic devices and components"
};

const newCategory = await createCategory(categoryData);
```

#### 3. Update Category
```javascript
import { updateCategory } from '@/services/admin/api';

const categoryData = {
  name: "Updated Electronics",
  description: "Updated description"
};

const updatedCategory = await updateCategory(1, categoryData);
```

#### 4. Delete Category
```javascript
import { deleteCategory } from '@/services/admin/api';

await deleteCategory(1);
```

### Sub-Category Functions

#### 1. Create Sub-Category
```javascript
import { createSubCategory } from '@/services/admin/api';

const subCategoryData = {
  name: "Smartphones",
  description: "Mobile phones and accessories",
  parent_id: 1 // ID of the parent category
};

const newSubCategory = await createSubCategory(subCategoryData);
```

#### 2. Update Sub-Category
```javascript
import { updateSubCategory } from '@/services/admin/api';

const subCategoryData = {
  name: "Updated Smartphones",
  description: "Updated description",
  parent_id: 1 // ID of the parent category
};

const updatedSubCategory = await updateSubCategory(2, subCategoryData);
```

#### 3. Delete Sub-Category
```javascript
import { deleteSubCategory } from '@/services/admin/api';

await deleteSubCategory(2);
```

## React Query Hooks

### Category Hooks

#### 1. useGetCategories
```javascript
import { useGetCategories } from '@/services/admin/query';

function CategoryList() {
  const { data, isLoading, error } = useGetCategories();
  
  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;
  
  const categories = data?.data || [];
  
  return (
    <div>
      {categories.map(category => (
        <div key={category.id}>{category.name}</div>
      ))}
    </div>
  );
}
```

#### 2. useCreateCategory
```javascript
import { useCreateCategory } from '@/services/admin/query';

function CreateCategoryForm() {
  const createCategoryMutation = useCreateCategory();
  
  const handleSubmit = async (formData) => {
    try {
      await createCategoryMutation.mutateAsync(formData);
      // Success handling
    } catch (error) {
      // Error handling
    }
  };
  
  return (
    <form onSubmit={handleSubmit}>
      {/* Form fields */}
    </form>
  );
}
```

#### 3. useUpdateCategory
```javascript
import { useUpdateCategory } from '@/services/admin/query';

function EditCategoryForm({ categoryId }) {
  const updateCategoryMutation = useUpdateCategory();
  
  const handleUpdate = async (formData) => {
    try {
      await updateCategoryMutation.mutateAsync({
        id: categoryId,
        categoryData: formData
      });
      // Success handling
    } catch (error) {
      // Error handling
    }
  };
  
  return (
    <form onSubmit={handleUpdate}>
      {/* Form fields */}
    </form>
  );
}
```

#### 4. useDeleteCategory
```javascript
import { useDeleteCategory } from '@/services/admin/query';

function CategoryActions({ categoryId }) {
  const deleteCategoryMutation = useDeleteCategory();
  
  const handleDelete = async () => {
    if (confirm('Are you sure?')) {
      try {
        await deleteCategoryMutation.mutateAsync(categoryId);
        // Success handling
      } catch (error) {
        // Error handling
      }
    }
  };
  
  return (
    <button onClick={handleDelete}>
      Delete Category
    </button>
  );
}
```

### Sub-Category Hooks

#### 1. useCreateSubCategory
```javascript
import { useCreateSubCategory } from '@/services/admin/query';

function CreateSubCategoryForm() {
  const createSubCategoryMutation = useCreateSubCategory();
  
  const handleSubmit = async (formData) => {
    try {
      await createSubCategoryMutation.mutateAsync({
        name: formData.name,
        description: formData.description,
        parent_id: formData.parentId // Required for sub-categories
      });
      // Success handling
    } catch (error) {
      // Error handling
    }
  };
  
  return (
    <form onSubmit={handleSubmit}>
      {/* Form fields including parent category selection */}
    </form>
  );
}
```

#### 2. useUpdateSubCategory
```javascript
import { useUpdateSubCategory } from '@/services/admin/query';

function EditSubCategoryForm({ subCategoryId }) {
  const updateSubCategoryMutation = useUpdateSubCategory();
  
  const handleUpdate = async (formData) => {
    try {
      await updateSubCategoryMutation.mutateAsync({
        id: subCategoryId,
        subCategoryData: {
          name: formData.name,
          description: formData.description,
          parent_id: formData.parentId
        }
      });
      // Success handling
    } catch (error) {
      // Error handling
    }
  };
  
  return (
    <form onSubmit={handleUpdate}>
      {/* Form fields */}
    </form>
  );
}
```

#### 3. useDeleteSubCategory
```javascript
import { useDeleteSubCategory } from '@/services/admin/query';

function SubCategoryActions({ subCategoryId }) {
  const deleteSubCategoryMutation = useDeleteSubCategory();
  
  const handleDelete = async () => {
    if (confirm('Are you sure?')) {
      try {
        await deleteSubCategoryMutation.mutateAsync(subCategoryId);
        // Success handling
      } catch (error) {
        // Error handling
      }
    }
  };
  
  return (
    <button onClick={handleDelete}>
      Delete Sub-Category
    </button>
  );
}
```

## Data Structure

### Category Response Structure
```json
{
  "success": true,
  "status_code": 200,
  "message": "Categories retrieved successfully",
  "data": [
    {
      "id": 1,
      "name": "Electronics",
      "description": "Electronic devices and components",
      "slug": "electronics",
      "status": 1,
      "image": null,
      "created_at": "2025-06-30T10:00:00.000000Z",
      "updated_at": "2025-06-30T10:00:00.000000Z",
      "sub_categories": [
        {
          "id": 2,
          "name": "Smartphones",
          "description": "Mobile phones and accessories",
          "slug": "smartphones",
          "status": 1,
          "image": null,
          "parent_id": 1,
          "created_at": "2025-06-30T10:00:00.000000Z",
          "updated_at": "2025-06-30T10:00:00.000000Z"
        }
      ]
    }
  ]
}
```

### Request Body Structure

#### Category Creation/Update
```json
{
  "name": "Category Name",
  "description": "Category description (optional)"
}
```

#### Sub-Category Creation/Update
```json
{
  "name": "Sub-Category Name",
  "description": "Sub-category description (optional)",
  "parent_id": 1
}
```

## Key Features

### 1. Automatic Cache Invalidation
- All mutations automatically invalidate relevant queries
- Sub-category operations invalidate both category and sub-category caches
- Ensures UI stays in sync with server state

### 2. Error Handling
- All hooks provide error states
- Mutations include try-catch blocks for proper error handling
- Toast notifications for user feedback

### 3. Loading States
- Query hooks provide loading states
- Mutation hooks provide pending states
- Can be used to show loading indicators

### 4. Optimistic Updates
- React Query automatically handles optimistic updates
- UI updates immediately while API call is in progress
- Rolls back on error

## Best Practices

1. **Always handle errors** in mutation callbacks
2. **Use loading states** to provide user feedback
3. **Validate parent_id** when creating/updating sub-categories
4. **Confirm destructive actions** like delete operations
5. **Invalidate related queries** after mutations
6. **Use toast notifications** for user feedback

## Example Usage

See the complete example in `src/examples/CategoryManagementExample.jsx` for a full implementation demonstrating all CRUD operations with proper error handling and user feedback.
