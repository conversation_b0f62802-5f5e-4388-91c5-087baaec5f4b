"use client";

import { useForm, Controller } from "react-hook-form";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Popover,
  PopoverTrigger,
  PopoverContent,
} from "@/components/ui/popover";
import { X } from "lucide-react";
import { useEffect, useState } from "react";
import { useGetVendorBusinessCategory } from "@/services/vendor/query";

export default function PersonalInfoForm({ onNext, initialData }) {
  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
    setValue,
  } = useForm({
    defaultValues: initialData,
  });

  const { data } = useGetVendorBusinessCategory();
  const categories = data?.data || [];
  const [selectedCategories, setSelectedCategories] = useState([]);
  const [open, setOpen] = useState(false);

  const onSubmit = (data) => {
    const selectedIds = selectedCategories.map((cat) => cat.id);
    const selectedNames = selectedCategories.map((cat) => cat.name);
    onNext({ 
      personalInfo: { 
        ...data, 
        businessCategory: selectedIds,
        businessCategoryNames: selectedNames 
      } 
    });
  };

  const toggleCategory = (category) => {
    const isSelected = selectedCategories.some((c) => c.id === category.id);
    if (isSelected) {
      const updated = selectedCategories.filter((c) => c.id !== category.id);
      setSelectedCategories(updated);
      setValue("business_category_ids[0]", updated.map((c) => c.id));
    } else if (selectedCategories.length < 3) {
      const updated = [...selectedCategories, category];
      setSelectedCategories(updated);
      setValue("business_category_ids[0]", updated.map((c) => c.id));
    }
  };

  const removeCategory = (id) => {
    const updated = selectedCategories.filter((c) => c.id !== id);
    setSelectedCategories(updated);
    setValue("business_category_ids[0]", updated.map((c) => c.id));
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-8 font-poppins">
      {/* First Name and Last Name */}
      <div className="flex flex-col md:flex-row justify-between gap-6 md:gap-12">
        <div className="w-full md:w-1/2">
          <Label htmlFor="firstName">First Name</Label>
          <Input
            id="firstName"
            {...register("firstName", { required: "First name is required" })}
          />
          {errors.firstName && (
            <p className="text-red-500">{errors.firstName.message}</p>
          )}
        </div>
        <div className="w-full md:w-1/2">
          <Label htmlFor="lastName">Last Name</Label>
          <Input
            id="lastName"
            {...register("lastName", { required: "Last name is required" })}
          />
          {errors.lastName && (
            <p className="text-red-500">{errors.lastName.message}</p>
          )}
        </div>
      </div>

      {/* Email and Phone Number */}
      <div className="flex flex-col md:flex-row justify-between gap-6 md:gap-12">
        <div className="w-full md:w-1/2">
          <Label htmlFor="email">Email</Label>
          <Input
            id="email"
            type="email"
            {...register("email", { required: "Email is required" })}
          />
          {errors.email && (
            <p className="text-red-500">{errors.email.message}</p>
          )}
        </div>
        <div className="w-full md:w-1/2">
          <Label htmlFor="phoneNumber">Phone Number</Label>
          <Input
            id="phoneNumber"
            {...register("phoneNumber", {
              required: "Phone number is required",
            })}
          />
          {errors.phoneNumber && (
            <p className="text-red-500">{errors.phoneNumber.message}</p>
          )}
        </div>
      </div>

      {/* Address and Company Type */}
      <div className="flex flex-col md:flex-row justify-between gap-6 md:gap-12">
        <div className="w-full md:w-1/2">
          <Label htmlFor="address">Address</Label>
          <Input
            id="address"
            {...register("address", { required: "Address is required" })}
          />
          {errors.address && (
            <p className="text-red-500">{errors.address.message}</p>
          )}
        </div>
        <div className="w-full md:w-1/2">
          <Label htmlFor="companyType">Company Type</Label>
          <Controller
            name="companyType"
            control={control}
            rules={{ required: "Company type is required" }}
            render={({ field }) => (
              <select
                id="companyType"
                {...field}
                className="w-full border rounded-md px-3 py-2"
              >
                <option value="">Select your company type</option>
                <option value="private">Private</option>
                <option value="semi-government">Semi-Government</option>
                <option value="government">Government</option>
              </select>
            )}
          />
          {errors.companyType && (
            <p className="text-red-500">{errors.companyType.message}</p>
          )}
        </div>
      </div>

      {/* Business Category as Chip Select */}
      <div className="w-full md:w-1/2">
        <Label htmlFor="businessCategory">Business Category</Label>
        <Popover open={open} onOpenChange={setOpen}>
          <PopoverTrigger asChild>
            <Button variant="outline" className="w-full flex-wrap min-h-[40px] justify-start">
              {selectedCategories.length > 0 ? (
                <div className="flex gap-2 flex-wrap">
                  {selectedCategories.map((cat) => (
                    <span
                      key={cat.id}
                      className="bg-primary text-white text-xs px-2 py-0.5 rounded-full flex items-center"
                    >
                      {cat.name}
                      <button
                        type="button"
                        onClick={(e) => {
                          e.stopPropagation();
                          removeCategory(cat.id);
                        }}
                        className="ml-1 hover:text-red-300"
                      >
                        <X size={14} />
                      </button>
                    </span>
                  ))}
                </div>
              ) : (
                <span className="text-muted-foreground">Select categories</span>
              )}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-[300px] max-h-[200px] overflow-auto p-2">
            {categories.map((category) => (
              <div
                key={category.id}
                className={`cursor-pointer px-3 py-2 rounded-md hover:bg-muted ${
                  selectedCategories.length >= 3 && !selectedCategories.find((c) => c.id === category.id)
                    ? 'opacity-50 pointer-events-none'
                    : ''
                }`}
                onClick={() => toggleCategory(category)}
              >
                {category.name}
              </div>
            ))}
          </PopoverContent>
        </Popover>
        {errors.businessCategory && (
          <p className="text-red-500">{errors.businessCategory.message}</p>
        )}
        {selectedCategories.length >= 3 && (
          <p className="text-sm text-muted-foreground mt-1">You can select up to 3 categories only.</p>
        )}
      </div>

      {/* Buttons */}
      <div className="flex flex-col md:flex-row justify-end gap-4 md:gap-6">
        <Button className="w-full md:w-48" type="button" variant="outline">
          Skip
        </Button>
        <Button className="w-full md:w-48 text-white" type="submit">
          Next
        </Button>
      </div>
    </form>
  );
}
