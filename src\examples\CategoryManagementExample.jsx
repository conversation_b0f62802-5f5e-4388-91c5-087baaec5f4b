/**
 * Example Component: Category and Sub-Category Management
 * This demonstrates how to use all the CRUD operations for categories and sub-categories
 */

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/components/ui/use-toast';
import {
  useGetCategories,
  useCreateCategory,
  useUpdateCategory,
  useDeleteCategory,
  useCreateSubCategory,
  useUpdateSubCategory,
  useDeleteSubCategory
} from '@/services/admin/query';

export default function CategoryManagementExample() {
  const { toast } = useToast();
  
  // State for forms
  const [categoryForm, setCategoryForm] = useState({ name: '', description: '' });
  const [subCategoryForm, setSubCategoryForm] = useState({ 
    name: '', 
    description: '', 
    parent_id: '' 
  });
  const [editingCategory, setEditingCategory] = useState(null);
  const [editingSubCategory, setEditingSubCategory] = useState(null);

  // Query hooks
  const { data: categoriesData, isLoading, error } = useGetCategories();
  
  // Mutation hooks
  const createCategoryMutation = useCreateCategory();
  const updateCategoryMutation = useUpdateCategory();
  const deleteCategoryMutation = useDeleteCategory();
  const createSubCategoryMutation = useCreateSubCategory();
  const updateSubCategoryMutation = useUpdateSubCategory();
  const deleteSubCategoryMutation = useDeleteSubCategory();

  const categories = categoriesData?.data || [];

  // Category CRUD handlers
  const handleCreateCategory = async (e) => {
    e.preventDefault();
    try {
      await createCategoryMutation.mutateAsync(categoryForm);
      toast({
        title: "Success",
        description: "Category created successfully",
      });
      setCategoryForm({ name: '', description: '' });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create category",
        variant: "destructive",
      });
    }
  };

  const handleUpdateCategory = async (e) => {
    e.preventDefault();
    try {
      await updateCategoryMutation.mutateAsync({
        id: editingCategory.id,
        categoryData: categoryForm
      });
      toast({
        title: "Success",
        description: "Category updated successfully",
      });
      setEditingCategory(null);
      setCategoryForm({ name: '', description: '' });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update category",
        variant: "destructive",
      });
    }
  };

  const handleDeleteCategory = async (id) => {
    if (window.confirm('Are you sure you want to delete this category?')) {
      try {
        await deleteCategoryMutation.mutateAsync(id);
        toast({
          title: "Success",
          description: "Category deleted successfully",
        });
      } catch (error) {
        toast({
          title: "Error",
          description: "Failed to delete category",
          variant: "destructive",
        });
      }
    }
  };

  // Sub-category CRUD handlers
  const handleCreateSubCategory = async (e) => {
    e.preventDefault();
    try {
      await createSubCategoryMutation.mutateAsync(subCategoryForm);
      toast({
        title: "Success",
        description: "Sub-category created successfully",
      });
      setSubCategoryForm({ name: '', description: '', parent_id: '' });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create sub-category",
        variant: "destructive",
      });
    }
  };

  const handleUpdateSubCategory = async (e) => {
    e.preventDefault();
    try {
      await updateSubCategoryMutation.mutateAsync({
        id: editingSubCategory.id,
        subCategoryData: subCategoryForm
      });
      toast({
        title: "Success",
        description: "Sub-category updated successfully",
      });
      setEditingSubCategory(null);
      setSubCategoryForm({ name: '', description: '', parent_id: '' });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update sub-category",
        variant: "destructive",
      });
    }
  };

  const handleDeleteSubCategory = async (id) => {
    if (window.confirm('Are you sure you want to delete this sub-category?')) {
      try {
        await deleteSubCategoryMutation.mutateAsync(id);
        toast({
          title: "Success",
          description: "Sub-category deleted successfully",
        });
      } catch (error) {
        toast({
          title: "Error",
          description: "Failed to delete sub-category",
          variant: "destructive",
        });
      }
    }
  };

  // Helper functions
  const startEditingCategory = (category) => {
    setEditingCategory(category);
    setCategoryForm({ name: category.name, description: category.description || '' });
  };

  const startEditingSubCategory = (subCategory) => {
    setEditingSubCategory(subCategory);
    setSubCategoryForm({ 
      name: subCategory.name, 
      description: subCategory.description || '',
      parent_id: subCategory.parent_id || ''
    });
  };

  const cancelEditing = () => {
    setEditingCategory(null);
    setEditingSubCategory(null);
    setCategoryForm({ name: '', description: '' });
    setSubCategoryForm({ name: '', description: '', parent_id: '' });
  };

  if (isLoading) return <div>Loading categories...</div>;
  if (error) return <div>Error loading categories: {error.message}</div>;

  return (
    <div className="space-y-6 p-6">
      <h1 className="text-2xl font-bold">Category Management Example</h1>
      
      {/* Category Form */}
      <Card>
        <CardHeader>
          <CardTitle>
            {editingCategory ? 'Edit Category' : 'Create New Category'}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={editingCategory ? handleUpdateCategory : handleCreateCategory} className="space-y-4">
            <div>
              <Label htmlFor="categoryName">Category Name</Label>
              <Input
                id="categoryName"
                value={categoryForm.name}
                onChange={(e) => setCategoryForm({ ...categoryForm, name: e.target.value })}
                required
              />
            </div>
            <div>
              <Label htmlFor="categoryDescription">Description</Label>
              <Input
                id="categoryDescription"
                value={categoryForm.description}
                onChange={(e) => setCategoryForm({ ...categoryForm, description: e.target.value })}
              />
            </div>
            <div className="flex gap-2">
              <Button 
                type="submit" 
                disabled={createCategoryMutation.isPending || updateCategoryMutation.isPending}
              >
                {editingCategory ? 'Update Category' : 'Create Category'}
              </Button>
              {editingCategory && (
                <Button type="button" variant="outline" onClick={cancelEditing}>
                  Cancel
                </Button>
              )}
            </div>
          </form>
        </CardContent>
      </Card>

      {/* Sub-Category Form */}
      <Card>
        <CardHeader>
          <CardTitle>
            {editingSubCategory ? 'Edit Sub-Category' : 'Create New Sub-Category'}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={editingSubCategory ? handleUpdateSubCategory : handleCreateSubCategory} className="space-y-4">
            <div>
              <Label htmlFor="subCategoryName">Sub-Category Name</Label>
              <Input
                id="subCategoryName"
                value={subCategoryForm.name}
                onChange={(e) => setSubCategoryForm({ ...subCategoryForm, name: e.target.value })}
                required
              />
            </div>
            <div>
              <Label htmlFor="subCategoryDescription">Description</Label>
              <Input
                id="subCategoryDescription"
                value={subCategoryForm.description}
                onChange={(e) => setSubCategoryForm({ ...subCategoryForm, description: e.target.value })}
              />
            </div>
            <div>
              <Label htmlFor="parentCategory">Parent Category</Label>
              <select
                id="parentCategory"
                value={subCategoryForm.parent_id}
                onChange={(e) => setSubCategoryForm({ ...subCategoryForm, parent_id: e.target.value })}
                className="w-full p-2 border rounded"
                required
              >
                <option value="">Select Parent Category</option>
                {categories.map((category) => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>
            <div className="flex gap-2">
              <Button 
                type="submit" 
                disabled={createSubCategoryMutation.isPending || updateSubCategoryMutation.isPending}
              >
                {editingSubCategory ? 'Update Sub-Category' : 'Create Sub-Category'}
              </Button>
              {editingSubCategory && (
                <Button type="button" variant="outline" onClick={cancelEditing}>
                  Cancel
                </Button>
              )}
            </div>
          </form>
        </CardContent>
      </Card>

      {/* Categories List */}
      <Card>
        <CardHeader>
          <CardTitle>Categories & Sub-Categories</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {categories.map((category) => (
              <div key={category.id} className="border p-4 rounded">
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="font-semibold">{category.name}</h3>
                    {category.description && (
                      <p className="text-sm text-gray-600">{category.description}</p>
                    )}
                  </div>
                  <div className="flex gap-2">
                    <Button 
                      size="sm" 
                      variant="outline" 
                      onClick={() => startEditingCategory(category)}
                    >
                      Edit
                    </Button>
                    <Button 
                      size="sm" 
                      variant="destructive" 
                      onClick={() => handleDeleteCategory(category.id)}
                      disabled={deleteCategoryMutation.isPending}
                    >
                      Delete
                    </Button>
                  </div>
                </div>
                
                {/* Sub-categories */}
                {category.sub_categories && category.sub_categories.length > 0 && (
                  <div className="mt-4 ml-4">
                    <h4 className="font-medium text-sm mb-2">Sub-categories:</h4>
                    {category.sub_categories.map((subCategory) => (
                      <div key={subCategory.id} className="flex justify-between items-center py-2 border-l-2 border-gray-200 pl-4">
                        <div>
                          <span className="text-sm">{subCategory.name}</span>
                          {subCategory.description && (
                            <p className="text-xs text-gray-500">{subCategory.description}</p>
                          )}
                        </div>
                        <div className="flex gap-1">
                          <Button 
                            size="sm" 
                            variant="outline" 
                            onClick={() => startEditingSubCategory(subCategory)}
                          >
                            Edit
                          </Button>
                          <Button 
                            size="sm" 
                            variant="destructive" 
                            onClick={() => handleDeleteSubCategory(subCategory.id)}
                            disabled={deleteSubCategoryMutation.isPending}
                          >
                            Delete
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
