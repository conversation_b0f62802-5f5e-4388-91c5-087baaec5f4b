import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  createNotice,
  deleteNotice,
  getNotices,
  updateNotice,
  getTrainingSessions,
  createTrainingSession,
  updateTrainingSession,
  deleteTrainingSession,
  getTrainingSessionById
} from "./api";

// Notice queries
export function useGetNotices() {
  return useQuery({
    queryKey: ["getNotices"],
    queryFn: () => getNotices(),
  });
}

// Create notice mutation
export const useCreateNotice = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (formData) => createNotice(formData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notices'] });
    },
  });
};

// Update notice mutation
export const useUpdateNotice = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }) => updateNotice(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notices'] });
    },
  });
};

export function useDeleteNotice() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id) => deleteNotice(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["getNotices"] });
    },
  });
}

// Training queries and mutations
export function useGetTrainingSessions() {
  return useQuery({
    queryKey: ["getTrainingSessions"],
    queryFn: () => getTrainingSessions(),
  });
}

export function useGetTrainingSessionById(id) {
  return useQuery({
    queryKey: ["getTrainingSession", id],
    queryFn: () => getTrainingSessionById(id),
    enabled: !!id,
  });
}

export const useCreateTrainingSession = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (trainingData) => createTrainingSession(trainingData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["getTrainingSessions"] });
    },
  });
};

export const useUpdateTrainingSession = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }) => updateTrainingSession(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["getTrainingSessions"] });
    },
  });
};

export function useDeleteTrainingSession() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id) => deleteTrainingSession(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["getTrainingSessions"] });
    },
  });
};