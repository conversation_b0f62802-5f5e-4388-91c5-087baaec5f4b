"use client"

import { useState } from "react"
import { Search, Eye, MessageSquare, Filter } from "lucide-react"
import { format } from "date-fns"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
    DialogFooter,
    DialogClose,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Separator } from "@/components/ui/separator"

// Mock data for specifications
const initialSpecifications = [
    {
        id: "SPEC-2023-001",
        customerName: "Acme Corp",
        category: "Electronics",
        subCategory: "Computers",
        productName: "High-Performance Workstation",
        specificationDetail:
            "Intel i9 processor, 32GB RAM, RTX 4080 graphics card, 1TB NVMe SSD, dual 4K monitors, professional keyboard and mouse. Must support CAD software and video editing applications...",
        submissionDate: new Date(2023, 5, 15),
        status: "submitted",
        remarks: "",
        fullSpecification: `
      **Hardware Requirements:**
      - Processor: Intel Core i9-13900K or equivalent
      - RAM: 32GB DDR5-5600
      - Graphics: NVIDIA RTX 4080 or better
      - Storage: 1TB NVMe SSD (PCIe 4.0)
      - Additional Storage: 2TB HDD for backup
      
      **Display Requirements:**
      - Dual 27" 4K monitors (3840x2160)
      - Color accuracy: 99% sRGB, 95% Adobe RGB
      - Refresh rate: 60Hz minimum
      
      **Peripherals:**
      - Mechanical keyboard with RGB backlighting
      - High-precision mouse (12000+ DPI)
      - Professional webcam (1080p minimum)
      - Noise-canceling headset
      
      **Software Compatibility:**
      - AutoCAD 2024
      - Adobe Creative Suite
      - SolidWorks 2023
      - Windows 11 Pro
      
      **Additional Requirements:**
      - Warranty: 3 years on-site
      - Setup and configuration included
      - Training for 2 users
      - Budget range: $8,000 - $12,000
    `,
    },
    {
        id: "SPEC-2023-002",
        customerName: "Globex Manufacturing",
        category: "Industrial Equipment",
        subCategory: "Machinery",
        productName: "CNC Milling Machine",
        specificationDetail:
            "3-axis CNC milling machine with automatic tool changer, spindle speed up to 12,000 RPM, working area 800x600x500mm, precision ±0.005mm...",
        submissionDate: new Date(2023, 5, 18),
        status: "in_review",
        remarks: "Technical team is reviewing the precision requirements.",
        fullSpecification: `
      **Machine Specifications:**
      - Type: 3-axis vertical CNC milling machine
      - Working area: 800mm x 600mm x 500mm (X x Y x Z)
      - Spindle speed: 100-12,000 RPM
      - Tool capacity: 20-tool automatic changer
      - Positioning accuracy: ±0.005mm
      - Repeatability: ±0.003mm
      
      **Control System:**
      - CNC controller: Fanuc or Siemens equivalent
      - Programming: G-code compatible
      - Interface: Touch screen HMI
      - Network connectivity: Ethernet
      
      **Power Requirements:**
      - Main spindle: 15kW
      - Total power consumption: 25kW
      - Voltage: 380V, 3-phase, 50Hz
      
      **Additional Features:**
      - Flood coolant system
      - Chip conveyor
      - Work light (LED)
      - Emergency stop systems
      - Safety interlocks
      
      **Installation & Support:**
      - Installation and commissioning
      - Operator training (40 hours)
      - 2-year warranty
      - Preventive maintenance schedule
      - Budget: $150,000 - $200,000
    `,
    },
    {
        id: "SPEC-2023-003",
        customerName: "TechStart Solutions",
        category: "Software",
        subCategory: "Development Tools",
        productName: "Custom ERP System",
        specificationDetail:
            "Web-based ERP system with modules for inventory, accounting, HR, and CRM. Must support 100+ concurrent users, cloud deployment, mobile responsive...",
        submissionDate: new Date(2023, 5, 20),
        status: "request_revisions",
        remarks: "Please provide more details about integration requirements and specific accounting standards needed.",
        fullSpecification: `
      **System Overview:**
      - Type: Web-based Enterprise Resource Planning (ERP)
      - Architecture: Cloud-native, microservices
      - Users: 100+ concurrent users
      - Deployment: AWS/Azure cloud platform
      
      **Core Modules:**
      1. Inventory Management
         - Real-time stock tracking
         - Barcode/QR code support
         - Automated reorder points
         - Multi-warehouse support
      
      2. Financial Management
         - General ledger
         - Accounts payable/receivable
         - Financial reporting
         - Tax compliance
      
      3. Human Resources
         - Employee records
         - Payroll processing
         - Leave management
         - Performance tracking
      
      4. Customer Relationship Management
         - Lead tracking
         - Sales pipeline
         - Customer support tickets
         - Marketing campaigns
      
      **Technical Requirements:**
      - Frontend: React.js or Angular
      - Backend: Node.js or Python
      - Database: PostgreSQL or MySQL
      - Mobile responsive design
      - API-first architecture
      - Single sign-on (SSO)
      
      **Integration Requirements:**
      - Third-party accounting software
      - Email marketing platforms
      - Payment gateways
      - Shipping providers
      
      **Timeline & Budget:**
      - Development: 8-12 months
      - Budget: $200,000 - $300,000
      - Ongoing support included
    `,
    },
    {
        id: "SPEC-2023-004",
        customerName: "Green Energy Co",
        category: "Renewable Energy",
        subCategory: "Solar Systems",
        productName: "Commercial Solar Installation",
        specificationDetail:
            "500kW commercial solar system with battery storage, grid-tie capability, monitoring system, 25-year warranty. Installation on flat roof...",
        submissionDate: new Date(2023, 5, 22),
        status: "approved",
        remarks: "Approved for vendor quotations. All technical requirements are clear.",
        fullSpecification: `
      **Solar System Specifications:**
      - Total capacity: 500kW DC
      - Panel type: Monocrystalline silicon
      - Panel efficiency: 21%+ 
      - Number of panels: ~1,250 (400W each)
      - Inverter type: String inverters or power optimizers
      
      **Battery Storage:**
      - Capacity: 1MWh lithium-ion
      - Depth of discharge: 90%
      - Cycle life: 6,000+ cycles
      - Warranty: 10 years
      
      **Installation Requirements:**
      - Roof type: Flat commercial roof
      - Roof area: 35,000 sq ft
      - Structural assessment required
      - Electrical upgrades included
      
      **Grid Connection:**
      - Grid-tie with net metering
      - Utility interconnection
      - Power quality monitoring
      - Remote shutdown capability
      
      **Monitoring & Maintenance:**
      - Real-time monitoring system
      - Mobile app access
      - Performance analytics
      - 25-year system warranty
      - Annual maintenance included
      
      **Compliance:**
      - UL listed components
      - Local building codes
      - Utility requirements
      - Environmental permits
      
      **Project Timeline:**
      - Design: 4-6 weeks
      - Permits: 8-12 weeks
      - Installation: 6-8 weeks
      - Budget: $400,000 - $500,000
    `,
    },
    {
        id: "SPEC-2023-005",
        customerName: "Metro Hospital",
        category: "Medical Equipment",
        subCategory: "Imaging",
        productName: "Digital X-Ray System",
        specificationDetail:
            "Digital radiography system with flat panel detector, mobile C-arm, DICOM compatibility, radiation dose optimization...",
        submissionDate: new Date(2023, 5, 25),
        status: "declined",
        remarks:
            "Budget constraints do not align with the specified requirements. Please consider a more cost-effective solution.",
        fullSpecification: `
      **X-Ray System Specifications:**
      - Type: Digital radiography (DR) system
      - Detector: Flat panel detector (17" x 17")
      - Resolution: 3.5 lp/mm minimum
      - Generator: High frequency, 50kW
      - Tube: Rotating anode, 0.6/1.2mm focal spots
      
      **Mobile C-Arm:**
      - C-arm rotation: ±110 degrees
      - Orbital rotation: ±180 degrees
      - SID range: 60-120cm
      - Image intensifier: 9" or 12"
      
      **Imaging Features:**
      - DICOM 3.0 compatibility
      - Automatic exposure control (AEC)
      - Radiation dose optimization
      - Image enhancement algorithms
      - Collimation system
      
      **Workstation:**
      - High-resolution monitors (2MP minimum)
      - Image processing software
      - PACS integration
      - CD/DVD burning capability
      - Network connectivity
      
      **Safety & Compliance:**
      - Lead shielding included
      - Radiation monitoring
      - FDA/CE certification
      - IEC 60601 compliance
      - Emergency stop systems
      
      **Installation & Training:**
      - Site preparation requirements
      - Installation and commissioning
      - Operator training (40 hours)
      - Service training for technicians
      - 2-year warranty
      
      **Budget:** $250,000 - $350,000
    `,
    },
]

// Status options with colors and labels
const statusOptions = [
    { value: "submitted", label: "Submitted", color: "bg-blue-100 text-blue-800" },
    { value: "in_review", label: "In Review", color: "bg-yellow-100 text-yellow-800" },
    { value: "request_revisions", label: "Request Revisions", color: "bg-orange-100 text-orange-800" },
    { value: "approved", label: "Approved", color: "bg-green-100 text-green-800" },
    { value: "declined", label: "Declined", color: "bg-red-100 text-red-800" },
]

// Categories for filtering
const categories = [...new Set(initialSpecifications.map((spec) => spec.category))]

export default function SpecificationManagementPage() {
    const [specifications, setSpecifications] = useState(initialSpecifications)
    const [searchQuery, setSearchQuery] = useState("")
    const [statusFilter, setStatusFilter] = useState("all")
    const [categoryFilter, setCategoryFilter] = useState("all")
    const [selectedSpec, setSelectedSpec] = useState(null)
    const [isDetailDialogOpen, setIsDetailDialogOpen] = useState(false)
    const [isStatusDialogOpen, setIsStatusDialogOpen] = useState(false)
    const [newStatus, setNewStatus] = useState("")
    const [statusRemarks, setStatusRemarks] = useState("")

    // Filter specifications based on search and filters
    const filteredSpecifications = specifications.filter((spec) => {
        const searchMatch =
            searchQuery === "" ||
            spec.customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
            spec.productName.toLowerCase().includes(searchQuery.toLowerCase()) ||
            spec.category.toLowerCase().includes(searchQuery.toLowerCase()) ||
            spec.id.toLowerCase().includes(searchQuery.toLowerCase())

        const statusMatch = statusFilter === "all" || spec.status === statusFilter
        const categoryMatch = categoryFilter === "all" || spec.category === categoryFilter

        return searchMatch && statusMatch && categoryMatch
    })

    // Handle opening specification details
    const handleViewDetails = (spec) => {
        setSelectedSpec(spec)
        setIsDetailDialogOpen(true)
    }

    // Handle opening status change dialog
    const handleChangeStatus = (spec) => {
        setSelectedSpec(spec)
        setNewStatus(spec.status)
        setStatusRemarks(spec.remarks || "")
        setIsStatusDialogOpen(true)
    }

    // Handle saving status change
    const handleSaveStatusChange = () => {
        setSpecifications(
            specifications.map((spec) =>
                spec.id === selectedSpec.id
                    ? {
                        ...spec,
                        status: newStatus,
                        remarks: statusRemarks,
                    }
                    : spec,
            ),
        )
        setIsStatusDialogOpen(false)
    }

    // Get status badge component
    const getStatusBadge = (status) => {
        const statusOption = statusOptions.find((option) => option.value === status)
        return (
            <Badge className={statusOption?.color} variant="secondary">
                {statusOption?.label}
            </Badge>
        )
    }

    // Truncate text for preview
    const truncateText = (text, maxLength = 100) => {
        return text.length > maxLength ? text.substring(0, maxLength) + "..." : text
    }

    // Reset all filters
    const resetFilters = () => {
        setSearchQuery("")
        setStatusFilter("all")
        setCategoryFilter("all")
    }

    return (
        <>
            <Card>
                <CardHeader className="flex flex-col sm:flex-row items-start sm:items-center justify-between space-y-2 sm:space-y-0 pb-4">
                    <CardTitle className="text-2xl font-bold">Specification Management</CardTitle>
                    <div className="flex items-center space-x-2">
                        <div className="relative w-full sm:w-64">
                            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                            <Input
                                type="search"
                                placeholder="Search specifications..."
                                className="pl-8"
                                value={searchQuery}
                                onChange={(e) => setSearchQuery(e.target.value)}
                            />
                        </div>
                    </div>
                </CardHeader>
                <CardContent>
                    {/* Filters */}
                    <div className="flex flex-wrap gap-4 mb-6">
                        <div className="flex items-center space-x-2">
                            <Label htmlFor="status-filter">Status:</Label>
                            <Select value={statusFilter} onValueChange={setStatusFilter}>
                                <SelectTrigger className="w-[180px]">
                                    <SelectValue placeholder="All Statuses" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">All Statuses</SelectItem>
                                    {statusOptions.map((status) => (
                                        <SelectItem key={status.value} value={status.value}>
                                            {status.label}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>

                        <div className="flex items-center space-x-2">
                            <Label htmlFor="category-filter">Category:</Label>
                            <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                                <SelectTrigger className="w-[180px]">
                                    <SelectValue placeholder="All Categories" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">All Categories</SelectItem>
                                    {categories.map((category) => (
                                        <SelectItem key={category} value={category}>
                                            {category}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>

                        <Button variant="outline" onClick={resetFilters}>
                            <Filter className="h-4 w-4 mr-2" />
                            Reset Filters
                        </Button>
                    </div>

                    {/* Specifications Table */}
                    <div className="rounded-md border">
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead>Spec ID</TableHead>
                                    <TableHead>Customer</TableHead>
                                    <TableHead>Category</TableHead>
                                    <TableHead>Product Name</TableHead>
                                    <TableHead className="hidden lg:table-cell">Specification Preview</TableHead>
                                    <TableHead>Submission Date</TableHead>
                                    <TableHead>Status</TableHead>
                                    <TableHead>Actions</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {filteredSpecifications.length === 0 ? (
                                    <TableRow>
                                        <TableCell colSpan={8} className="text-center py-8 text-muted-foreground">
                                            No specifications found matching your criteria
                                        </TableCell>
                                    </TableRow>
                                ) : (
                                    filteredSpecifications.map((spec) => (
                                        <TableRow key={spec.id} className="hover:bg-muted/50">
                                            <TableCell className="font-medium">{spec.id}</TableCell>
                                            <TableCell>{spec.customerName}</TableCell>
                                            <TableCell>
                                                <div>
                                                    <div className="font-medium">{spec.category}</div>
                                                    <div className="text-sm text-muted-foreground">{spec.subCategory}</div>
                                                </div>
                                            </TableCell>
                                            <TableCell className="max-w-[200px]">
                                                <div className="truncate" title={spec.productName}>
                                                    {spec.productName}
                                                </div>
                                            </TableCell>
                                            <TableCell className="hidden lg:table-cell max-w-[300px]">
                                                <div className="text-sm text-muted-foreground">{truncateText(spec.specificationDetail)}</div>
                                            </TableCell>
                                            <TableCell>{format(spec.submissionDate, "MMM dd, yyyy")}</TableCell>
                                            <TableCell>{getStatusBadge(spec.status)}</TableCell>
                                            <TableCell>
                                                <div className="flex items-center gap-2">
                                                    <Button variant="ghost" size="icon" onClick={() => handleViewDetails(spec)}>
                                                        <Eye className="h-4 w-4" />
                                                        <span className="sr-only">View Details</span>
                                                    </Button>
                                                    <Button variant="ghost" size="icon" onClick={() => handleChangeStatus(spec)}>
                                                        <MessageSquare className="h-4 w-4" />
                                                        <span className="sr-only">Change Status</span>
                                                    </Button>
                                                </div>
                                            </TableCell>
                                        </TableRow>
                                    ))
                                )}
                            </TableBody>
                        </Table>
                    </div>
                </CardContent>
            </Card>

            {/* Specification Details Dialog */}
            <Dialog open={isDetailDialogOpen} onOpenChange={setIsDetailDialogOpen}>
                {selectedSpec && (
                    <DialogContent className="sm:max-w-[800px] max-h-[80vh] overflow-y-auto">
                        <DialogHeader>
                            <DialogTitle>{selectedSpec.productName}</DialogTitle>
                            <DialogDescription>
                                Specification ID: {selectedSpec.id} | Customer: {selectedSpec.customerName}
                            </DialogDescription>
                        </DialogHeader>
                        <div className="py-4 space-y-4">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <Label className="text-sm font-medium">Category</Label>
                                    <p className="text-sm text-muted-foreground">
                                        {selectedSpec.category} → {selectedSpec.subCategory}
                                    </p>
                                </div>
                                <div>
                                    <Label className="text-sm font-medium">Submission Date</Label>
                                    <p className="text-sm text-muted-foreground">
                                        {format(selectedSpec.submissionDate, "MMMM dd, yyyy")}
                                    </p>
                                </div>
                                <div>
                                    <Label className="text-sm font-medium">Current Status</Label>
                                    <div className="mt-1">{getStatusBadge(selectedSpec.status)}</div>
                                </div>
                                <div>
                                    <Label className="text-sm font-medium">Customer</Label>
                                    <p className="text-sm text-muted-foreground">{selectedSpec.customerName}</p>
                                </div>
                            </div>

                            <Separator />

                            <div>
                                <Label className="text-sm font-medium">Full Specification Details</Label>
                                <div className="mt-2 p-4 bg-muted/50 rounded-md">
                                    <pre className="whitespace-pre-wrap text-sm font-mono">{selectedSpec.fullSpecification}</pre>
                                </div>
                            </div>

                            <Separator />

                            {selectedSpec.remarks && (
                                <div>
                                    <Label className="text-sm font-medium">Admin Remarks</Label>
                                    <p className="mt-1 text-sm text-muted-foreground p-3 bg-yellow-50 rounded-md border-l-4 border-yellow-400">
                                        {selectedSpec.remarks}
                                    </p>
                                </div>
                            )}
                        </div>
                        <DialogFooter>
                            <Button variant="outline" onClick={() => handleChangeStatus(selectedSpec)}>
                                Change Status
                            </Button>
                            <DialogClose asChild>
                                <Button>Close</Button>
                            </DialogClose>
                        </DialogFooter>
                    </DialogContent>
                )}
            </Dialog>

            {/* Status Change Dialog */}
            <Dialog open={isStatusDialogOpen} onOpenChange={setIsStatusDialogOpen}>
                {selectedSpec && (
                    <DialogContent className="sm:max-w-[500px]">
                        <DialogHeader>
                            <DialogTitle>Change Specification Status</DialogTitle>
                            <DialogDescription>Update the status for specification: {selectedSpec.id}</DialogDescription>
                        </DialogHeader>
                        <div className="py-4 space-y-4">
                            <div>
                                <Label htmlFor="new-status">New Status</Label>
                                <Select value={newStatus} onValueChange={setNewStatus}>
                                    <SelectTrigger className="mt-1">
                                        <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {statusOptions.map((status) => (
                                            <SelectItem key={status.value} value={status.value}>
                                                {status.label}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>

                            <div>
                                <Label htmlFor="status-remarks">Remarks (Optional)</Label>
                                <Textarea
                                    id="status-remarks"
                                    value={statusRemarks}
                                    onChange={(e) => setStatusRemarks(e.target.value)}
                                    placeholder="Add any comments or feedback for the customer..."
                                    className="mt-1"
                                    rows={4}
                                />
                            </div>
                        </div>
                        <DialogFooter>
                            <DialogClose asChild>
                                <Button variant="outline">Cancel</Button>
                            </DialogClose>
                            <Button onClick={handleSaveStatusChange}>Update Status</Button>
                        </DialogFooter>
                    </DialogContent>
                )}
            </Dialog>
        </>
    )
}