"use client"

import { useState } from "react"
import { Search, Eye, MessageSquare, Filter, Loader2, ChevronLeft, ChevronRight } from "lucide-react"
import { format } from "date-fns"

import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
    DialogFooter,
    DialogClose,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Separator } from "@/components/ui/separator"
import { useToast } from "@/components/ui/use-toast"
import { useGetAdminSpecifications, useUpdateSpecificationStatus } from "@/services/admin/query"

// Status options with colors and labels - updated to match API response
const statusOptions = [
    { value: "draft", label: "Draft", color: "bg-gray-100 text-gray-800" },
    { value: "published", label: "Published", color: "bg-green-100 text-green-800" },
    { value: "archived", label: "Archived", color: "bg-red-100 text-red-800" },
]

export default function SpecificationManagementPage() {
    const { toast } = useToast()
    const [currentPage, setCurrentPage] = useState(1)
    const [searchQuery, setSearchQuery] = useState("")
    const [statusFilter, setStatusFilter] = useState("all")
    const [categoryFilter, setCategoryFilter] = useState("all")
    const [selectedSpec, setSelectedSpec] = useState(null)
    const [isDetailDialogOpen, setIsDetailDialogOpen] = useState(false)
    const [isStatusDialogOpen, setIsStatusDialogOpen] = useState(false)
    const [newStatus, setNewStatus] = useState("")
    const [statusRemarks, setStatusRemarks] = useState("")

    // API hooks
    const { data: specificationsData, isLoading, error, refetch } = useGetAdminSpecifications(currentPage, 15)
    const updateStatusMutation = useUpdateSpecificationStatus()

    const specifications = specificationsData?.data || []
    const pagination = specificationsData?.pagination || {}

    // Extract unique categories from specifications for filtering
    const categories = [...new Set(specifications.map((spec) => spec.category?.name).filter(Boolean))]

    // Filter specifications based on search and filters
    const filteredSpecifications = specifications.filter((spec) => {
        const searchMatch =
            searchQuery === "" ||
            spec.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
            spec.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
            spec.category?.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
            spec.id.toString().toLowerCase().includes(searchQuery.toLowerCase())

        const statusMatch = statusFilter === "all" || spec.status === statusFilter
        const categoryMatch = categoryFilter === "all" || spec.category?.name === categoryFilter

        return searchMatch && statusMatch && categoryMatch
    })

    // Handle opening specification details
    const handleViewDetails = (spec) => {
        setSelectedSpec(spec)
        setIsDetailDialogOpen(true)
    }

    // Handle opening status change dialog
    const handleChangeStatus = (spec) => {
        setSelectedSpec(spec)
        setNewStatus(spec.status)
        setStatusRemarks(spec.remarks || "")
        setIsStatusDialogOpen(true)
    }

    // Handle saving status change
    const handleSaveStatusChange = async () => {
        try {
            await updateStatusMutation.mutateAsync({
                id: selectedSpec.id,
                statusData: { status: newStatus }
            })
            toast({
                title: "Success",
                description: "Specification status updated successfully",
            })
            setIsStatusDialogOpen(false)
        } catch {
            toast({
                title: "Error",
                description: "Failed to update specification status",
                variant: "destructive",
            })
        }
    }

    // Get status badge component
    const getStatusBadge = (status) => {
        const statusOption = statusOptions.find((option) => option.value === status)
        return (
            <Badge className={statusOption?.color} variant="secondary">
                {statusOption?.label}
            </Badge>
        )
    }

    // Truncate text for preview
    const truncateText = (text, maxLength = 100) => {
        return text.length > maxLength ? text.substring(0, maxLength) + "..." : text
    }

    // Reset all filters
    const resetFilters = () => {
        setSearchQuery("")
        setStatusFilter("all")
        setCategoryFilter("all")
    }

    return (
        <>
            <Card>
                <CardHeader className="flex flex-col sm:flex-row items-start sm:items-center justify-between space-y-2 sm:space-y-0 pb-4">
                    <CardTitle className="text-2xl font-bold">Specification Management</CardTitle>
                    <div className="flex items-center space-x-2">
                        <div className="relative w-full sm:w-64">
                            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                            <Input
                                type="search"
                                placeholder="Search specifications..."
                                className="pl-8"
                                value={searchQuery}
                                onChange={(e) => setSearchQuery(e.target.value)}
                            />
                        </div>
                    </div>
                </CardHeader>
                <CardContent>
                    {/* Filters */}
                    <div className="flex flex-wrap gap-4 mb-6">
                        <div className="flex items-center space-x-2">
                            <Label htmlFor="status-filter">Status:</Label>
                            <Select value={statusFilter} onValueChange={setStatusFilter}>
                                <SelectTrigger className="w-[180px]">
                                    <SelectValue placeholder="All Statuses" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">All Statuses</SelectItem>
                                    {statusOptions.map((status) => (
                                        <SelectItem key={status.value} value={status.value}>
                                            {status.label}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>

                        <div className="flex items-center space-x-2">
                            <Label htmlFor="category-filter">Category:</Label>
                            <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                                <SelectTrigger className="w-[180px]">
                                    <SelectValue placeholder="All Categories" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">All Categories</SelectItem>
                                    {categories.map((category) => (
                                        <SelectItem key={category} value={category}>
                                            {category}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>

                        <Button variant="outline" onClick={resetFilters}>
                            <Filter className="h-4 w-4 mr-2" />
                            Reset Filters
                        </Button>
                    </div>

                    {/* Specifications Table */}
                    <div className="rounded-md border">
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead>Spec ID</TableHead>
                                    <TableHead>Title</TableHead>
                                    <TableHead>Category</TableHead>
                                    <TableHead>Type</TableHead>
                                    <TableHead className="hidden lg:table-cell">Description</TableHead>
                                    <TableHead>Items Count</TableHead>
                                    <TableHead>Created Date</TableHead>
                                    <TableHead>Status</TableHead>
                                    <TableHead>Actions</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {isLoading ? (
                                    <TableRow>
                                        <TableCell colSpan={9} className="text-center py-8">
                                            <Loader2 className="h-6 w-6 animate-spin mx-auto" />
                                            <p className="mt-2 text-muted-foreground">Loading specifications...</p>
                                        </TableCell>
                                    </TableRow>
                                ) : error ? (
                                    <TableRow>
                                        <TableCell colSpan={9} className="text-center py-8">
                                            <div className="flex flex-col items-center space-y-2">
                                                <p className="text-red-600">Error loading specifications</p>
                                                <Button variant="outline" size="sm" onClick={() => refetch()}>
                                                    Try Again
                                                </Button>
                                            </div>
                                        </TableCell>
                                    </TableRow>
                                ) : filteredSpecifications.length === 0 ? (
                                    <TableRow>
                                        <TableCell colSpan={9} className="text-center py-8 text-muted-foreground">
                                            No specifications found matching your criteria
                                        </TableCell>
                                    </TableRow>
                                ) : (
                                    filteredSpecifications.map((spec) => (
                                        <TableRow key={spec.id} className="hover:bg-muted/50">
                                            <TableCell className="font-medium">{spec.id}</TableCell>
                                            <TableCell className="max-w-[200px]">
                                                <div className="truncate" title={spec.title}>
                                                    {spec.title}
                                                </div>
                                            </TableCell>
                                            <TableCell>
                                                <div>
                                                    <div className="font-medium">{spec.category?.name || 'N/A'}</div>
                                                    {spec.sub_category && (
                                                        <div className="text-sm text-muted-foreground">{spec.sub_category.name}</div>
                                                    )}
                                                </div>
                                            </TableCell>
                                            <TableCell>
                                                <Badge variant="outline" className="capitalize">
                                                    {spec.type}
                                                </Badge>
                                            </TableCell>
                                            <TableCell className="hidden lg:table-cell max-w-[300px]">
                                                <div className="text-sm text-muted-foreground">{truncateText(spec.description)}</div>
                                            </TableCell>
                                            <TableCell>{spec.items_count}</TableCell>
                                            <TableCell>{format(new Date(spec.created_at), "MMM dd, yyyy")}</TableCell>
                                            <TableCell>{getStatusBadge(spec.status)}</TableCell>
                                            <TableCell>
                                                <div className="flex items-center gap-2">
                                                    <Button variant="ghost" size="icon" onClick={() => handleViewDetails(spec)}>
                                                        <Eye className="h-4 w-4" />
                                                        <span className="sr-only">View Details</span>
                                                    </Button>
                                                    <Button variant="ghost" size="icon" onClick={() => handleChangeStatus(spec)}>
                                                        <MessageSquare className="h-4 w-4" />
                                                        <span className="sr-only">Change Status</span>
                                                    </Button>
                                                </div>
                                            </TableCell>
                                        </TableRow>
                                    ))
                                )}
                            </TableBody>
                        </Table>
                    </div>

                    {/* Pagination */}
                    {pagination && pagination.total > 0 && (
                        <div className="flex items-center justify-between px-2 py-4">
                            <div className="text-sm text-muted-foreground">
                                Showing {((pagination.current_page - 1) * pagination.per_page) + 1} to{" "}
                                {Math.min(pagination.current_page * pagination.per_page, pagination.total)} of{" "}
                                {pagination.total} results
                            </div>
                            <div className="flex items-center space-x-2">
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => setCurrentPage(currentPage - 1)}
                                    disabled={currentPage <= 1}
                                >
                                    <ChevronLeft className="h-4 w-4" />
                                    Previous
                                </Button>
                                <div className="flex items-center space-x-1">
                                    {Array.from({ length: Math.min(5, pagination.last_page) }, (_, i) => {
                                        const page = i + 1;
                                        return (
                                            <Button
                                                key={page}
                                                variant={currentPage === page ? "default" : "outline"}
                                                size="sm"
                                                onClick={() => setCurrentPage(page)}
                                            >
                                                {page}
                                            </Button>
                                        );
                                    })}
                                </div>
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => setCurrentPage(currentPage + 1)}
                                    disabled={currentPage >= pagination.last_page}
                                >
                                    Next
                                    <ChevronRight className="h-4 w-4" />
                                </Button>
                            </div>
                        </div>
                    )}
                </CardContent>
            </Card>

            {/* Specification Details Dialog */}
            <Dialog open={isDetailDialogOpen} onOpenChange={setIsDetailDialogOpen}>
                {selectedSpec && (
                    <DialogContent className="sm:max-w-[800px] max-h-[80vh] overflow-y-auto">
                        <DialogHeader>
                            <DialogTitle>{selectedSpec.title}</DialogTitle>
                            <DialogDescription>
                                Specification ID: {selectedSpec.id} | Type: {selectedSpec.type}
                            </DialogDescription>
                        </DialogHeader>
                        <div className="py-4 space-y-4">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <Label className="text-sm font-medium">Category</Label>
                                    <p className="text-sm text-muted-foreground">
                                        {selectedSpec.category?.name || 'N/A'}
                                        {selectedSpec.sub_category && ` → ${selectedSpec.sub_category.name}`}
                                    </p>
                                </div>
                                <div>
                                    <Label className="text-sm font-medium">Created Date</Label>
                                    <p className="text-sm text-muted-foreground">
                                        {format(new Date(selectedSpec.created_at), "MMMM dd, yyyy")}
                                    </p>
                                </div>
                                <div>
                                    <Label className="text-sm font-medium">Current Status</Label>
                                    <div className="mt-1">{getStatusBadge(selectedSpec.status)}</div>
                                </div>
                                <div>
                                    <Label className="text-sm font-medium">Items Count</Label>
                                    <p className="text-sm text-muted-foreground">{selectedSpec.items_count}</p>
                                </div>
                            </div>

                            <Separator />

                            <div>
                                <Label className="text-sm font-medium">Description</Label>
                                <div className="mt-2 p-4 bg-muted/50 rounded-md">
                                    <p className="text-sm">{selectedSpec.description}</p>
                                </div>
                            </div>

                            <Separator />

                            <div>
                                <Label className="text-sm font-medium">Specification Items</Label>
                                <div className="mt-2 space-y-3">
                                    {selectedSpec.items?.map((item) => (
                                        <div key={item.id} className="p-4 border rounded-md">
                                            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                                                <div>
                                                    <Label className="text-xs font-medium text-muted-foreground">Item Name</Label>
                                                    <p className="text-sm font-medium">{item.item_name}</p>
                                                </div>
                                                <div>
                                                    <Label className="text-xs font-medium text-muted-foreground">Quantity</Label>
                                                    <p className="text-sm">{item.quantity} {item.unit}</p>
                                                </div>
                                                <div className="md:col-span-2">
                                                    <Label className="text-xs font-medium text-muted-foreground">Specifications</Label>
                                                    <p className="text-sm">{item.specifications}</p>
                                                </div>
                                                {item.other && (
                                                    <div className="md:col-span-2">
                                                        <Label className="text-xs font-medium text-muted-foreground">Other Requirements</Label>
                                                        <p className="text-sm">{item.other}</p>
                                                    </div>
                                                )}
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </div>
                        <DialogFooter>
                            <Button variant="outline" onClick={() => handleChangeStatus(selectedSpec)}>
                                Change Status
                            </Button>
                            <DialogClose asChild>
                                <Button>Close</Button>
                            </DialogClose>
                        </DialogFooter>
                    </DialogContent>
                )}
            </Dialog>

            {/* Status Change Dialog */}
            <Dialog open={isStatusDialogOpen} onOpenChange={setIsStatusDialogOpen}>
                {selectedSpec && (
                    <DialogContent className="sm:max-w-[500px]">
                        <DialogHeader>
                            <DialogTitle>Change Specification Status</DialogTitle>
                            <DialogDescription>Update the status for specification: {selectedSpec.id}</DialogDescription>
                        </DialogHeader>
                        <div className="py-4 space-y-4">
                            <div>
                                <Label htmlFor="new-status">New Status</Label>
                                <Select value={newStatus} onValueChange={setNewStatus}>
                                    <SelectTrigger className="mt-1">
                                        <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {statusOptions.map((status) => (
                                            <SelectItem key={status.value} value={status.value}>
                                                {status.label}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>

                            <div>
                                <Label htmlFor="status-remarks">Remarks (Optional)</Label>
                                <Textarea
                                    id="status-remarks"
                                    value={statusRemarks}
                                    onChange={(e) => setStatusRemarks(e.target.value)}
                                    placeholder="Add any comments or feedback for the customer..."
                                    className="mt-1"
                                    rows={4}
                                />
                            </div>
                        </div>
                        <DialogFooter>
                            <DialogClose asChild>
                                <Button variant="outline" disabled={updateStatusMutation.isPending}>Cancel</Button>
                            </DialogClose>
                            <Button
                                onClick={handleSaveStatusChange}
                                disabled={updateStatusMutation.isPending}
                            >
                                {updateStatusMutation.isPending ? (
                                    <>
                                        <Loader2 className="h-4 w-4 animate-spin mr-2" />
                                        Updating...
                                    </>
                                ) : (
                                    'Update Status'
                                )}
                            </Button>
                        </DialogFooter>
                    </DialogContent>
                )}
            </Dialog>
        </>
    )
}