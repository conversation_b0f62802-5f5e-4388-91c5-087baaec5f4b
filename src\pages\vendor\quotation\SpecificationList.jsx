"use client"

import { useState } from "react"
import { Search, Filter, Calendar, Building, Tag } from "lucide-react"
import { format } from "date-fns"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Skeleton } from "@/components/ui/skeleton"

import { QuotationFormModal } from "./_components/QuotationFormModal"
import { SidebarTrigger } from "@/components/ui/sidebar"
import { Separator } from "@/components/ui/separator"
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList } from "@/components/ui/breadcrumb"
import { useGetSpecifications, useGetVendorBusinessCategory } from "@/services/vendor/query"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { useGetVendorProfileQuery } from "@/services/auth/query"

export function SpecificationsListVendor() {
  const [search, setSearch] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [categoryFilter, setCategoryFilter] = useState("all")
  const [page, setPage] = useState(1)
  const [selectedSpec, setSelectedSpec] = useState(null)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [openCollapsibleId, setOpenCollapsibleId] = useState(null)
  const [filtersOpen, setFiltersOpen] = useState(true)

  const { data: categoryData, isLoading: isCategoryLoading, error: categoryError } = useGetVendorProfileQuery()

  const categories = categoryData?.business_categories?.length
    ? ["all", ...categoryData.business_categories.map((cat) => cat.name)]
    : ["all"]

  const { data, isLoading, error } = useGetSpecifications({
    page,
    search,
    status: statusFilter,
    category: categoryFilter,
  })

  const handlePreviousPage = () => setPage((prev) => Math.max(prev - 1, 1))
  const handleNextPage = () => setPage((prev) => prev + 1)

  const toggleCollapsible = (specId) => {
    setOpenCollapsibleId((prev) => (prev === specId ? null : specId))
  }

  const getStatusColor = (status) => {
    switch (status) {
      case "published": return "bg-green-100 text-green-800 hover:bg-green-200"
      case "quoted": return "bg-blue-100 text-blue-800 hover:bg-blue-200"
      case "closed": return "bg-gray-100 text-gray-800 hover:bg-gray-200"
      default: return "bg-gray-100 text-gray-800 hover:bg-gray-200"
    }
  }

  if (error || categoryError) {
    return (
      <div className="text-center py-8">
        <p className="text-red-600">{error?.message || categoryError?.message || "Error loading data. Please try again."}</p>
      </div>
    )
  }

  return (
    <>
      <header className="flex h-12 items-center gap-2 px-4">
        <SidebarTrigger className="-ml-1" />
        <Separator orientation="vertical" className="h-4" />
        <Breadcrumb className="font-nunito">
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href="#">Request Quotation</BreadcrumbLink>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
      </header>

      <div className="p-4">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-4">
          <div>
            <h1 className="text-3xl font-bold">Product Specifications</h1>
            <p className="text-muted-foreground">Browse and quote on customer requirements</p>
          </div>
        </div>

        <Collapsible open={filtersOpen} onOpenChange={setFiltersOpen}>
          <CollapsibleTrigger asChild>
            <Button variant="outline" className="mb-4">
              {filtersOpen ? "Hide Filters" : "Show Filters"}
            </Button>
          </CollapsibleTrigger>

          <CollapsibleContent>
            <div className="flex flex-col sm:flex-row flex-wrap gap-4 mb-8">
              <div className="relative flex-1 min-w-[200px]">
                <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input
                  placeholder="Search specifications..."
                  value={search}
                  onChange={(e) => setSearch(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-full sm:w-[180px]">
                  <Filter className="h-4 w-4 mr-2" />
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="published">Published</SelectItem>
                  <SelectItem value="quoted">Quoted</SelectItem>
                  <SelectItem value="closed">Closed</SelectItem>
                </SelectContent>
              </Select>
              {isCategoryLoading ? (
                <Skeleton className="w-full sm:w-[180px] h-10" />
              ) : (
                <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                  <SelectTrigger className="w-full sm:w-[180px]">
                    <Tag className="h-4 w-4 mr-2" />
                    <SelectValue placeholder="Filter by category" />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map((category) => (
                      <SelectItem key={category} value={category}>
                        {category === "all" ? "All Categories" : category}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            </div>

            {(statusFilter !== "all" || categoryFilter !== "all" || search) && (
              <div className="flex flex-wrap gap-2 pt-2">
                <div className="text-sm text-muted-foreground">Active filters:</div>
                {statusFilter !== "all" && (
                  <Badge variant="secondary" className="flex items-center gap-1">
                    Status: {statusFilter}
                    <button onClick={() => setStatusFilter("all")} className="ml-1 hover:bg-muted rounded-full p-0.5">✕</button>
                  </Badge>
                )}
                {categoryFilter !== "all" && (
                  <Badge variant="secondary" className="flex items-center gap-1">
                    Category: {categoryFilter}
                    <button onClick={() => setCategoryFilter("all")} className="ml-1 hover:bg-muted rounded-full p-0.5">✕</button>
                  </Badge>
                )}
                {search && (
                  <Badge variant="secondary" className="flex items-center gap-1">
                    Search: {search}
                    <button onClick={() => setSearch("")} className="ml-1 hover:bg-muted rounded-full p-0.5">✕</button>
                  </Badge>
                )}
                <Button variant="ghost" size="sm" onClick={() => { setStatusFilter("all"); setCategoryFilter("all"); setSearch(""); setPage(1); }} className="text-xs">
                  Clear all
                </Button>
              </div>
            )}
          </CollapsibleContent>
        </Collapsible>

        {isLoading ? (
          <div className="grid gap-6">
            {[...Array(3)].map((_, i) => (
              <Card key={i}>
                <CardHeader>
                  <Skeleton className="h-6 w-3/4" />
                  <Skeleton className="h-4 w-1/2" />
                </CardHeader>
                <CardContent>
                  <Skeleton className="h-20 w-full" />
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <div className="grid gap-6">
            {data?.data?.map((spec) => (
              <Card key={spec.id} className="hover:shadow-md transition-shadow">
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div className="space-y-2">
                      <CardTitle className="text-xl">{spec.title}</CardTitle>
                      <div className="flex flex-wrap items-center gap-4 text-sm text-muted-foreground">
                        <div className="flex items-center gap-1">
                          <Building className="h-4 w-4" />
                          {spec.customerName || "Unknown Customer"}
                        </div>
                        <div className="flex items-center gap-1">
                          <Calendar className="h-4 w-4" />
                          {format(new Date(spec.created_at), "MMM dd, yyyy")}
                        </div>
                      </div>
                    </div>
                    <Badge className={getStatusColor(spec.status)}>
                      {spec.status.charAt(0).toUpperCase() + spec.status.slice(1)}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground mb-4 line-clamp-3">{spec.description}</p>
                  {spec.items?.length > 0 && (
                    <Collapsible open={openCollapsibleId === spec.id} onOpenChange={() => toggleCollapsible(spec.id)}>
                      <CollapsibleTrigger asChild>
                        <Button variant="ghost" size="sm" className="mb-2">
                          {openCollapsibleId === spec.id ? "Hide Items" : `Show ${spec.items_count} Items`}
                        </Button>
                      </CollapsibleTrigger>
                      <CollapsibleContent>
                        <ul className="list-disc pl-5 text-sm space-y-2">
                          {spec.items.map((item) => (
                            <li key={item.id}>
                              <span className="font-medium">{item.item_name}</span> ({item.quantity} {item.unit})
                              <p className="text-muted-foreground">{item.specifications}</p>
                              {item.other && <p className="text-muted-foreground text-xs">Other: {item.other}</p>}
                            </li>
                          ))}
                        </ul>
                      </CollapsibleContent>
                    </Collapsible>
                  )}
                  <div className="flex justify-end mt-4">
                    <Button onClick={() => { setSelectedSpec(spec); setIsModalOpen(true); }} disabled={spec.status === "closed"}>
                      {spec.status === "quoted" ? "Update Quote" : "Submit Quote"}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {data?.data?.length === 0 && !isLoading && (
          <div className="text-center py-12">
            <p className="text-muted-foreground">No specifications found matching your criteria.</p>
          </div>
        )}

        {data?.data?.length > 0 && (
          <div className="flex justify-between items-center mt-6">
            <Button variant="outline" size="sm" onClick={handlePreviousPage} disabled={page === 1}>Previous</Button>
            <div className="text-sm text-muted-foreground">
              Page {page} of {data?.pagination?.last_page || 1} ({data?.pagination?.total || 0} items)
            </div>
            <Button variant="outline" size="sm" onClick={handleNextPage} disabled={page >= (data?.pagination?.last_page || 1)}>Next</Button>
          </div>
        )}

        <QuotationFormModal specification={selectedSpec} isOpen={isModalOpen} onClose={() => { setIsModalOpen(false); setSelectedSpec(null); }} />
      </div>
    </>
  )
}

export default SpecificationsListVendor;
